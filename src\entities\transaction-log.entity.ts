import { ApiProperty } from '@nestjs/swagger';
import {
	Column,
	CreateDateColumn,
	Entity,
	PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('transaction_logs')
export class TransactionLog {
	@ApiProperty({
		description: 'Unique transaction log identifier (UUID)',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@PrimaryGeneratedColumn('uuid')
	id: string;

	@ApiProperty({
		description: 'Action performed',
		example: 'CREATE_PAYMENT',
	})
	@Column({ type: 'varchar', length: 255 })
	action: string;

	@ApiProperty({
		description: 'Entity type that was affected',
		example: 'Payment',
	})
	@Column({ type: 'varchar', length: 255 })
	entity: string;

	@ApiProperty({
		description: 'ID of the affected entity',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@Column({ type: 'varchar', length: 255 })
	entityId: string;

	@ApiProperty({
		description: 'Additional data related to the action (JSON)',
		example: { orderId: 'ORD-12345', amount: 99.99 },
		required: false,
	})
	@Column({ type: 'jsonb', nullable: true })
	payload: Record<string, any>;

	@ApiProperty({
		description: 'Transaction log creation timestamp',
		example: '2025-09-03T10:00:00.000Z',
	})
	@CreateDateColumn()
	createdAt: Date;
}
