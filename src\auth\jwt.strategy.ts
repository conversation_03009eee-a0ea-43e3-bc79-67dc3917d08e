import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { type Algorithm } from 'jsonwebtoken';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { RoleType } from 'src/constants/role.js';
import { TokenType } from 'src/constants/token-type.js';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(
		private readonly configService: ConfigService,
	) {
		const publicKey = configService.get<string>('JWT_PUBLIC_KEY');
		if (!publicKey) {
			throw new Error('JWT_PUBLIC_KEY is not defined in environment variables');
		}
		
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			secretOrKey: publicKey.replaceAll(String.raw`\n`, '\n'),
			algorithms: [configService.get<string>('JWT_ALGORITHM') || 'RS256'] as Algorithm[],
		});
	}

	async validate(args: {
		userId: number;
		role: RoleType;
		type: TokenType;
	}): Promise<any> {
		const isUser =
			args.role === RoleType.USER && args.type === TokenType.ACCESS_TOKEN;
		const isSsoUser =
			args.role === RoleType.SSO_USER && args.type === TokenType.SSO_TOKEN;
		const isAdmin =
			args.role === RoleType.ADMIN && args.type === TokenType.ACCESS_TOKEN;

		if (!(isUser || isSsoUser || isAdmin)) {
			throw new UnauthorizedException();
		}


		if (!args.userId) {
			throw new UnauthorizedException();
		}

		return args;
	}
}