import { ConflictException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModuleOptions, JwtOptionsFactory } from '@nestjs/jwt';

export class JwtConfigService implements JwtOptionsFactory {
	constructor(private configService: ConfigService) {}
	private get(key: string): string {
		const value = this.configService.get<string>(key);

		if (value == null) {
			throw new NotFoundException(`Environment variable ${key} is not set`);
		}

		return value;
	}
    
	public getNumber(key: string): number {
		const value = this.get(key);
		const num = Number(value);

		if (Number.isNaN(num)) {
			throw new ConflictException(
				`Environment variable ${key} must be a number. Received: ${value}`,
			);
		}

		return num;
	}

	public getString(key: string): string {
		const value = this.get(key);

		return value.replaceAll(String.raw`\n`, '\n');
	}
	
	get authConfig() {
		return {
			privateKey: this.getString('JWT_PRIVATE_KEY'),
			publicKey: this.getString('JWT_PUBLIC_KEY'),
			jwtExpirationTime: this.getNumber('JWT_EXPIRATION_TIME'),
			jwtRefreshTokenExpirationTime: this.getNumber(
				'JWT_REFRESH_TOKEN_EXPIRATION_TIME',
			),
			'algorithm': 'RS256',
		};
	}

	createJwtOptions(): JwtModuleOptions {
		return {
			privateKey: this.authConfig.privateKey,
			publicKey: this.authConfig.publicKey,
			signOptions: {
				algorithm: 'RS256',
				expiresIn: this.authConfig.jwtExpirationTime,
			},
			verifyOptions: {
				algorithms: ['RS256'],
			},
		};
	}
}