import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';

import { PaymentModule } from '../payment/payment.module';
import { PaymentQueueProcessor } from './payment-queue.processor';
import { PaymentQueueService } from './payment-queue.service';

@Module({
	imports: [
		BullModule.registerQueue({
			name: 'payment_queue',
		}),
		PaymentModule,
	],
	providers: [PaymentQueueService, PaymentQueueProcessor],
	exports: [PaymentQueueService],
})
export class QueueModule {}
