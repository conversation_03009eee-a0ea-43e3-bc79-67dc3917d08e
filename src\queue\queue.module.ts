import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';

import { PaymentModule } from '../payment/payment.module.js';
import { PaymentQueueProcessor } from './payment-queue.processor.js';
import { PaymentQueueService } from './payment-queue.service.js';

@Module({
	imports: [
		BullModule.registerQueue({
			name: 'payment_queue',
		}),
		PaymentModule,
	],
	providers: [PaymentQueueService, PaymentQueueProcessor],
	exports: [PaymentQueueService],
})
export class QueueModule {}
