import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { JwtStrategy } from './jwt.strategy';
import { JwtConfigService } from './jwt-config.service';

@Module({
	imports: [
		ConfigModule.forRoot({ isGlobal: true }),
		PassportModule.register({ defaultStrategy: 'jwt' }),
		JwtModule.registerAsync({
			useFactory: async(configService: ConfigService) => {
				const jwtConfigService = new JwtConfigService(configService);
				return jwtConfigService.createJwtOptions();
			},
			inject: [ConfigService],
		}),
		PassportModule,
	],
	controllers: [],
	providers: [
		JwtConfigService,
		JwtStrategy,
	],
	exports: [JwtModule],
})
export class AuthModule {}