import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PaymentService } from '../payment/payment.service';
import { PaymentQueueService } from '../queue/payment-queue.service';

@Injectable()
export class PaymentScheduleService {
	private readonly logger = new Logger(PaymentScheduleService.name);

	constructor(
		private readonly paymentService: PaymentService,
		private readonly paymentQueueService: PaymentQueueService,
	) {}

	// Run every 10 minutes
	@Cron('0 */10 * * * *', {
		name: 'processPendingPayments',
		timeZone: 'UTC',
	})
	async handlePendingPayments(): Promise<void> {
		this.logger.log('Starting scheduled check for pending payments...');

		try {
			const pendingPayments =
				await this.paymentService.findPendingPayments();

			this.logger.log(`Found ${pendingPayments.length} pending payments`);

			for (const payment of pendingPayments) {
				// Add payment to queue for processing
				await this.paymentQueueService.addPaymentJob({
					paymentId: payment.txId,
					packKey: payment.packKey,
					action: 'PROCESS_PAYMENT',
				});

				this.logger.log(
					`Added payment ${payment.txId} (pack: ${payment.packKey}) to processing queue`,
				);
			}

			// Log the scheduled action
			// await this.transactionLogService.logAction(
			// 	'SCHEDULED_PENDING_PAYMENTS_CHECK',
			// 	'System',
			// 	'scheduler',
			// 	{
			// 		pendingCount: pendingPayments.length,
			// 		processedAt: new Date().toISOString(),
			// 	},
			// );

			this.logger.log(
				`Scheduled check completed. Queued ${pendingPayments.length} payments for processing`,
			);
		} catch(error: any) {
			this.logger.error(
				'Error during scheduled pending payments check:',
				error,
			);

			// await this.transactionLogService.logAction(
			// 	'SCHEDULED_PENDING_PAYMENTS_CHECK_FAILED',
			// 	'System',
			// 	'scheduler',
			// 	{
			// 		error: (error as Error).message,
			// 		processedAt: new Date().toISOString(),
			// 	},
			// );
		}
	}

	// Additional cron job for cleanup (runs daily at midnight)
	@Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
		name: 'dailyCleanup',
		timeZone: 'UTC',
	})
	async handleDailyCleanup(): Promise<void> {
		this.logger.log('Running daily cleanup tasks...');

		// Here you can add cleanup logic like:
		// - Archive old transaction logs
		// - Clean up failed jobs
		// - Generate daily reports

		// await this.transactionLogService.logAction(
		// 	'DAILY_CLEANUP',
		// 	'System',
		// 	'scheduler',
		// 	{
		// 		processedAt: new Date().toISOString(),
		// 	},
		// );

		this.logger.log('Daily cleanup completed');
	}
}
