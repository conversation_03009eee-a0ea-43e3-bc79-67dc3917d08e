import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { PaymentStatus, PaymentTransactionEntity } from '../entities/payment.entity.js';
import { CreatePaymentDto, UpdatePaymentDto } from './dto/payment.dto.js';

@Injectable()
export class PaymentService {
	constructor(
		@InjectRepository(PaymentTransactionEntity)
		private readonly paymentRepository: Repository<PaymentTransactionEntity>,
	) {}

	async create(createPaymentDto: CreatePaymentDto): Promise<PaymentTransactionEntity> {
		const payment = this.paymentRepository.create(createPaymentDto);
		const savedPayment = await this.paymentRepository.save(payment);

		// Log the payment creation
		// await this.transactionLogService.logAction(
		// 	'CREATE_PAYMENT',
		// 	'PaymentTransaction',
		// 	savedPayment.txId,
		// 	{ packKey: savedPayment.packKey, amount: savedPayment.amount },
		// );

		return savedPayment;
	}

	async findById(txId: string): Promise<PaymentTransactionEntity> {
		const payment = await this.paymentRepository.findOne({ where: { txId } });
		if (!payment) {
			throw new NotFoundException(`Payment with txId ${txId} not found`);
		}
		return payment;
	}

	async findByPackKey(packKey: string): Promise<PaymentTransactionEntity> {
		const payment = await this.paymentRepository.findOne({
			where: { packKey },
		});
		if (!payment) {
			throw new NotFoundException(
				`Payment with packKey ${packKey} not found`,
			);
		}
		return payment;
	}

	async updateStatus(
		txId: string,
		updatePaymentDto: UpdatePaymentDto,
	): Promise<PaymentTransactionEntity> {
		const payment = await this.findById(txId);
		// const oldStatus = payment.status;

		payment.status = updatePaymentDto.status;
		const updatedPayment = await this.paymentRepository.save(payment);

		// Log the status update
		// await this.transactionLogService.logAction(
		// 	'UPDATE_PAYMENT_STATUS',
		// 	'PaymentTransaction',
		// 	payment.txId,
		// 	{
		// 		packKey: payment.packKey,
		// 		oldStatus,
		// 		newStatus: updatePaymentDto.status,
		// 	},
		// );

		return updatedPayment;
	}

	async findPendingPayments(): Promise<PaymentTransactionEntity[]> {
		return await this.paymentRepository.find({
			where: { status: PaymentStatus.PENDING },
		});
	}

	async findAll(): Promise<PaymentTransactionEntity[]> {
		return await this.paymentRepository.find();
	}
}
