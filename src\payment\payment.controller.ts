import {
	Body,
	Controller,
	Get,
	HttpStatus,
	Param,
	ParseUUIDPipe,
	Post,
	Put,
	ValidationPipe,
} from '@nestjs/common';
import {
	ApiBody,
	ApiOperation,
	ApiParam,
	ApiResponse,
	ApiTags,
} from '@nestjs/swagger';
import { RoleType } from 'src/constants/role.js';
import { Auth } from 'src/decorators/http.decorators.js';

import { PaymentTransactionEntity } from '../entities/payment.entity.js';
import { CreatePaymentDto, UpdatePaymentDto } from './dto/payment.dto.js';
import { PaymentService } from './payment.service.js';

@ApiTags('Payments')
@Controller('payments')
export class PaymentController {
	constructor(private readonly paymentService: PaymentService) {}

	@Post()
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Create a new payment' })
	@ApiBody({ type: CreatePaymentDto })
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: 'Payment created successfully',
		type: PaymentTransactionEntity,
	})
	@ApiResponse({
		status: HttpStatus.BAD_REQUEST,
		description: 'Invalid payment data',
	})
	async create(
		@Body(ValidationPipe) createPaymentDto: CreatePaymentDto,
	): Promise<PaymentTransactionEntity> {
		return this.paymentService.create(createPaymentDto);
	}

	@Get()
	// @UseGuards(JwtAuthGuard)
	@Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get all payments' })
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Returns all payments',
		type: [PaymentTransactionEntity],
	})
	async findAll(): Promise<PaymentTransactionEntity[]> {
		return this.paymentService.findAll();
	}

	@Get(':txId')
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get payment by txId' })
	@ApiParam({
		name: 'txId',
		description: 'Payment transaction UUID',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Payment found',
		type: PaymentTransactionEntity,
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: 'Payment not found',
	})
	async findById(@Param('txId', ParseUUIDPipe) txId: string): Promise<PaymentTransactionEntity> {
		return this.paymentService.findById(txId);
	}

	@Get('pack/:packKey')
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Get payment by pack key' })
	@ApiParam({
		name: 'packKey',
		description: 'Pack key identifier',
		example: 'PACK-001',
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Payment found',
		type: PaymentTransactionEntity,
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: 'Payment not found',
	})
	async findByPackKey(@Param('packKey') packKey: string): Promise<PaymentTransactionEntity> {
		return this.paymentService.findByPackKey(packKey);
	}

	@Put(':txId/status')
	// @Auth([RoleType.USER])
	@ApiOperation({ summary: 'Update payment status' })
	@ApiParam({
		name: 'txId',
		description: 'Payment transaction UUID',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@ApiBody({ type: UpdatePaymentDto })
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Payment status updated successfully',
		type: PaymentTransactionEntity,
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: 'Payment not found',
	})
	@ApiResponse({
		status: HttpStatus.BAD_REQUEST,
		description: 'Invalid status value',
	})
	async updateStatus(
		@Param('txId', ParseUUIDPipe) txId: string,
		@Body(ValidationPipe) updatePaymentDto: UpdatePaymentDto,
	): Promise<PaymentTransactionEntity> {
		return this.paymentService.updateStatus(txId, updatePaymentDto);
	}
}
