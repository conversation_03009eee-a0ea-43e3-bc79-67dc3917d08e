import {
	Column,
	Entity,
	Index,
	PrimaryGeneratedColumn,
} from 'typeorm';

export enum PaymentStatus {
	PENDING = 'PENDING',
	SUCCESS = 'SUCCESS',
	FAILED = 'FAILED',
}

@Index('idx_payment_game', ['gameId'], {})
@Index('payment_transaction_pack_key', ['packKey'])
@Index('idx_payment_status', ['status'], {})
@Index('payment_transaction_pkey', ['txId'], { unique: true })
@Index('idx_payment_user', ['userId'], {})
@Entity('payment_transaction', { schema: 'public' })
export class PaymentTransactionEntity {
	@PrimaryGeneratedColumn('uuid', { name: 'tx_id' })
	txId!: string;

	@Column('bigint', { name: 'user_id' })
	userId!: number;

	@Column('integer', { name: 'game_id', nullable: true })
	gameId?: number;

	@Column('character varying', { name: 'pack_key' })
	packKey!: string;

	@Column('numeric', { name: 'amount', precision: 10, scale: 2 })
	amount!: number;

	@Column('character varying', { name: 'currency', length: 10, default: 'VND' })
	currency!: string;

	@Column('character varying', { name: 'payment_method', length: 30 })
	paymentMethod!: string;

	@Column('character varying', {
		name: 'status',
		length: 30,
		default: PaymentStatus.PENDING,
	})
	status!: string;

	@Column('character varying', { name: 'note', length: 100 })
	note!: string;

	@Column('timestamp without time zone', {
		name: 'created_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare createdAt: Date;

	@Column('timestamp without time zone', {
		name: 'updated_at',
		default: () => 'CURRENT_TIMESTAMP',
	})
	declare updatedAt: Date;

}
