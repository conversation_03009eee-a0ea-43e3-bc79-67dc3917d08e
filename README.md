# FS Payment Service

A comprehensive payment processing service built with NestJS, TypeScript, PostgreSQL, and Redis. This service provides a robust foundation for handling payment operations with transaction logging, queue processing, and scheduled tasks.

## 🏗️ Architecture Overview

This payment service follows clean architecture principles with the following modules:

### Core Modules

- **Payment Module**: Handles payment creation, status updates, and retrieval
- **Transaction Log Module**: Tracks all system events and changes
- **Queue Module**: Manages background job processing with Redis
- **Schedule Module**: Runs periodic tasks (cron jobs)

### Key Features

- ✅ **Payment Processing**: Create, update, and track payment statuses
- ✅ **Transaction Logging**: Complete transaction trail for all operations
- ✅ **Queue System**: Background job processing with Bull/Redis
- ✅ **Scheduled Tasks**: Automatic processing of pending payments every 10 minutes
- ✅ **Clean Architecture**: Separation of entities, DTOs, services, and controllers
- ✅ **Database Integration**: PostgreSQL with TypeORM
- ✅ **Validation**: Input validation with class-validator
- ✅ **Error Handling**: Comprehensive error handling and logging

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- Redis server
- Yarn package manager

### Installation

1. **Clone or download the project**
2. **Install dependencies:**
   ```bash
   yarn install
   ```

3. **Set up environment variables:**
   The application uses the following environment variables (automatically configured in Replit):
   - `DATABASE_URL`: PostgreSQL connection string
   - `PGHOST`, `PGPORT`, `PGUSER`, `PGPASSWORD`, `PGDATABASE`: PostgreSQL connection details
   - `PORT`: Application port (default: 5000)

4. **Start Redis server:**
   ```bash
   redis-server
   ```

5. **Run the application:**
   ```bash
   # Development mode with hot-reload
   yarn start:dev
   
   # Production mode
   yarn build
   yarn start:prod
   ```

6. **Access Swagger UI for API testing:**
   ```
   http://localhost:5000/api
   ```

## 🔍 Swagger UI for API Testing

This application includes a comprehensive Swagger UI documentation and testing interface:

- **Swagger UI URL**: `http://localhost:5000/api`
- **OpenAPI JSON**: `http://localhost:5000/api-json`
- **Features**:
  - Interactive API testing
  - Automatic request/response documentation
  - Example payloads for all endpoints
  - Schema definitions for all DTOs and entities

### Using Swagger UI

1. Navigate to `http://localhost:5000/api` in your browser
2. Browse through the available endpoints organized by tags (payments, transaction-logs)
3. Click "Try it out" on any endpoint to test it
4. Fill in the required parameters and request body
5. Click "Execute" to make the API call
6. View the response including status code, headers, and body

## 📚 API Endpoints

### Payment Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/payments` | Create a new payment |
| GET | `/payments` | Get all payments |
| GET | `/payments/:id` | Get payment by ID |
| GET | `/payments/order/:orderId` | Get payment by order ID |
| PUT | `/payments/:id/status` | Update payment status |

### Transaction Log Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/transaction-logs` | Get all transaction logs |
| GET | `/transaction-logs/entity/:entity` | Get logs by entity type |
| GET | `/transaction-logs/entity-id/:entityId` | Get logs by entity ID |

### Example API Usage

**Create a Payment:**
```bash
curl -X POST http://localhost:5000/payments \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "ORD-12345",
    "amount": 99.99,
    "currency": "USD"
  }'
```

**Update Payment Status:**
```bash
curl -X PUT http://localhost:5000/payments/{payment-id}/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "SUCCESS"
  }'
```

## 🗄️ Database Schema

### Payment Entity
```typescript
{
  id: string;          // UUID primary key
  orderId: string;     // Unique order identifier
  amount: number;      // Payment amount (decimal)
  currency: string;    // Currency code (default: USD)
  status: enum;        // PENDING | SUCCESS | FAILED
  createdAt: Date;     // Creation timestamp
  updatedAt: Date;     // Last update timestamp
}
```

### Transaction Log Entity
```typescript
{
  id: string;               // UUID primary key
  action: string;           // Action performed
  entity: string;           // Entity type
  entityId: string;         // Entity ID
  payload: object;          // Additional data (JSON)
  createdAt: Date;          // Creation timestamp
}
```

## 🔄 Queue System

The service uses Redis with Bull for queue management:

- **Queue Name**: `payment_queue`
- **Job Types**:
  - `PROCESS_PAYMENT`: Process a payment
  - `RETRY_PAYMENT`: Retry a failed payment
  - `NOTIFY_PAYMENT`: Send payment notifications

### Queue Features
- ✅ Job retry with exponential backoff
- ✅ Dead letter queue for failed jobs
- ✅ Job progress tracking
- ✅ Automatic cleanup

## ⏰ Scheduled Tasks

### Pending Payment Processor
- **Schedule**: Every 10 minutes (`0 */10 * * * *`)
- **Function**: Finds all payments with `PENDING` status and queues them for processing
- **Logging**: All scheduling activities are logged in transaction logs

### Daily Cleanup
- **Schedule**: Daily at midnight
- **Function**: Performs maintenance tasks (placeholder for future enhancements)

## 🛠️ Development Commands

```bash
# Install dependencies
yarn install

# Start development server
yarn start:dev

# Build for production
yarn build

# Start production server
yarn start:prod

# Run tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run end-to-end tests
yarn test:e2e

# Format code
yarn format

# Lint code
yarn lint
```

## 🔧 Configuration

### TypeORM Configuration
- Database synchronization enabled in development
- Entities auto-loaded from `/entities` directory
- Migration support for production deployments

### Redis Configuration
- Default connection: `localhost:6379`
- Configurable via environment variables
- Connection pooling enabled

### Validation
- Global validation pipes enabled
- DTO validation with class-validator
- Whitelist unknown properties

## 📁 Project Structure

```
src/
├── entities/           # Database entities
│   ├── payment.entity.ts
│   └── transaction-log.entity.ts
├── payment/           # Payment module
│   ├── dto/
│   ├── payment.controller.ts
│   ├── payment.service.ts
│   └── payment.module.ts
├── transaction-log/         # Transaction logging module
│   ├── transaction-log.controller.ts
│   ├── transaction-log.service.ts
│   └── transaction-log.module.ts
├── queue/             # Queue processing module
│   ├── payment-queue.service.ts
│   ├── payment-queue.processor.ts
│   └── queue.module.ts
├── schedule/          # Scheduled tasks module
│   ├── payment-schedule.service.ts
│   └── schedule.module.ts
├── database/          # Database configuration
│   └── database.config.ts
├── app.module.ts      # Main application module
└── main.ts           # Application bootstrap
```

## 🔧 Integration Guides for Future Modules

### 📋 How to Integrate Transaction Logging

When creating new modules that need transaction logging functionality, follow these steps:

#### 1. Import TransactionLogModule in your module

```typescript
// your-new-module.module.ts
import { Module } from '@nestjs/common';
import { TransactionLogModule } from '../transaction-log/transaction-log.module';
import { YourNewService } from './your-new.service';
import { YourNewController } from './your-new.controller';

@Module({
  imports: [TransactionLogModule], // Import transaction log module
  providers: [YourNewService],
  controllers: [YourNewController],
})
export class YourNewModule {}
```

#### 2. Inject TransactionLogService in your service

```typescript
// your-new.service.ts
import { Injectable } from '@nestjs/common';
import { TransactionLogService } from '../transaction-log/transaction-log.service';

@Injectable()
export class YourNewService {
  constructor(
    private readonly transactionLogService: TransactionLogService,
  ) {}

  async createSomething(data: any): Promise<any> {
    // Your business logic here
    const result = await this.performSomeOperation(data);

    // Log the action
    await this.transactionLogService.logAction(
      'CREATE_SOMETHING',           // Action name
      'YourEntity',                 // Entity type
      result.id,                    // Entity ID
      {                            // Additional payload
        originalData: data,
        resultData: result,
        timestamp: new Date().toISOString(),
      },
    );

    return result;
  }

  async updateSomething(id: string, updateData: any): Promise<any> {
    const existingEntity = await this.findById(id);
    const updatedEntity = await this.performUpdate(id, updateData);

    // Log the update with before/after data
    await this.transactionLogService.logAction(
      'UPDATE_SOMETHING',
      'YourEntity',
      id,
      {
        before: existingEntity,
        after: updatedEntity,
        changes: updateData,
      },
    );

    return updatedEntity;
  }

  async deleteSomething(id: string): Promise<void> {
    const entityToDelete = await this.findById(id);
    await this.performDelete(id);

    // Log the deletion
    await this.transactionLogService.logAction(
      'DELETE_SOMETHING',
      'YourEntity',
      id,
      {
        deletedData: entityToDelete,
        deletedAt: new Date().toISOString(),
      },
    );
  }
}
```

#### 3. Best Practices for Transaction Logging

```typescript
// Common transaction action naming patterns
'CREATE_ENTITY_NAME'     // e.g., 'CREATE_USER', 'CREATE_ORDER'
'UPDATE_ENTITY_NAME'     // e.g., 'UPDATE_USER', 'UPDATE_ORDER'
'DELETE_ENTITY_NAME'     // e.g., 'DELETE_USER', 'DELETE_ORDER'
'APPROVE_ENTITY_NAME'    // e.g., 'APPROVE_PAYMENT', 'APPROVE_ORDER'
'REJECT_ENTITY_NAME'     // e.g., 'REJECT_PAYMENT', 'REJECT_ORDER'

// System actions
'SYSTEM_CLEANUP'
'SYSTEM_BACKUP'
'SCHEDULED_TASK_EXECUTED'

// Error actions
'OPERATION_FAILED'
'VALIDATION_ERROR'
'EXTERNAL_API_ERROR'
```

### 🚀 How to Integrate Redis Queue System

When adding background job processing to new modules, follow these integration steps:

#### 1. Import QueueModule in your module

```typescript
// your-new-module.module.ts
import { Module } from '@nestjs/common';
import { QueueModule } from '../queue/queue.module';
import { YourNewService } from './your-new.service';
import { YourNewQueueProcessor } from './your-new-queue.processor';

@Module({
  imports: [QueueModule], // Import queue module
  providers: [YourNewService, YourNewQueueProcessor],
})
export class YourNewModule {}
```

#### 2. Create a dedicated queue for your module

```typescript
// your-new-queue.service.ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import type { Queue } from 'bull';

export interface YourNewQueueJob {
  entityId: string;
  action: 'PROCESS_ENTITY' | 'SEND_NOTIFICATION' | 'CLEANUP_DATA';
  data?: Record<string, any>;
}

@Injectable()
export class YourNewQueueService {
  constructor(
    @InjectQueue('your_new_queue') private readonly yourNewQueue: Queue,
  ) {}

  async addProcessJob(entityId: string, data?: any): Promise<void> {
    await this.yourNewQueue.add('process-entity', {
      entityId,
      action: 'PROCESS_ENTITY',
      data,
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    });
  }

  async addNotificationJob(entityId: string, notificationData: any): Promise<void> {
    await this.yourNewQueue.add('send-notification', {
      entityId,
      action: 'SEND_NOTIFICATION',
      data: notificationData,
    }, {
      delay: 5000, // Delay 5 seconds before processing
    });
  }

  async addCleanupJob(entityId: string): Promise<void> {
    await this.yourNewQueue.add('cleanup-data', {
      entityId,
      action: 'CLEANUP_DATA',
    }, {
      delay: 24 * 60 * 60 * 1000, // Delay 24 hours
    });
  }
}
```

#### 3. Create a queue processor

```typescript
// your-new-queue.processor.ts
import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import type { Job } from 'bull';
import { YourNewQueueJob } from './your-new-queue.service';
import { YourNewService } from './your-new.service';
import { TransactionLogService } from '../transaction-log/transaction-log.service';

@Processor('your_new_queue')
export class YourNewQueueProcessor {
  private readonly logger = new Logger(YourNewQueueProcessor.name);

  constructor(
    private readonly yourNewService: YourNewService,
    private readonly transactionLogService: TransactionLogService,
  ) {}

  @Process('process-entity')
  async handleProcessEntity(job: Job<YourNewQueueJob>): Promise<void> {
    const { entityId, data } = job.data;
    
    this.logger.log(`Processing entity: ${entityId}`);

    try {
      await this.yourNewService.processEntity(entityId, data);

      // Log successful processing
      await this.transactionLogService.logAction(
        'QUEUE_PROCESS_ENTITY_SUCCESS',
        'YourEntity',
        entityId,
        { jobId: job.id, data },
      );

      this.logger.log(`Successfully processed entity: ${entityId}`);
    } catch (error) {
      this.logger.error(`Failed to process entity: ${entityId}`, error);
      
      // Log failed processing
      await this.transactionLogService.logAction(
        'QUEUE_PROCESS_ENTITY_FAILED',
        'YourEntity',
        entityId,
        { jobId: job.id, error: error.message },
      );
      
      throw error; // Re-throw to trigger retry mechanism
    }
  }

  @Process('send-notification')
  async handleSendNotification(job: Job<YourNewQueueJob>): Promise<void> {
    const { entityId, data } = job.data;
    
    try {
      await this.yourNewService.sendNotification(entityId, data);
      
      await this.transactionLogService.logAction(
        'QUEUE_NOTIFICATION_SENT',
        'YourEntity',
        entityId,
        { jobId: job.id, notificationType: data.type },
      );
    } catch (error) {
      this.logger.error(`Failed to send notification for entity: ${entityId}`, error);
      throw error;
    }
  }
}
```

#### 4. Register your queue in app.module.ts

```typescript
// app.module.ts
@Module({
  imports: [
    // ... other imports
    BullModule.registerQueue({
      name: 'your_new_queue', // Add your new queue here
    }),
  ],
})
export class AppModule {}
```

#### 5. Use the queue service in your business logic

```typescript
// your-new.service.ts
import { Injectable } from '@nestjs/common';
import { YourNewQueueService } from './your-new-queue.service';

@Injectable()
export class YourNewService {
  constructor(
    private readonly yourNewQueueService: YourNewQueueService,
  ) {}

  async createEntity(data: any): Promise<any> {
    // Create entity synchronously
    const entity = await this.saveEntity(data);

    // Queue background processing
    await this.yourNewQueueService.addProcessJob(entity.id, {
      createdAt: new Date().toISOString(),
      originalData: data,
    });

    // Queue notification (delayed)
    await this.yourNewQueueService.addNotificationJob(entity.id, {
      type: 'ENTITY_CREATED',
      recipientEmail: data.email,
    });

    return entity;
  }
}
```

### 🔄 Queue Management Best Practices

1. **Job Naming**: Use descriptive job names and consistent patterns
2. **Error Handling**: Always implement proper error handling and logging
3. **Retries**: Configure appropriate retry strategies with exponential backoff
4. **Monitoring**: Log job progress and failures for monitoring
5. **Cleanup**: Implement cleanup jobs for old data
6. **Testing**: Test queue processors separately from main business logic

### 📊 Monitoring Queue Health

```typescript
// Example health check for queues
export class QueueHealthService {
  async checkQueueHealth(): Promise<boolean> {
    const queueStats = await this.yourNewQueue.getJobCounts();
    
    // Alert if too many failed jobs
    if (queueStats.failed > 100) {
      await this.transactionLogService.logAction(
        'QUEUE_HEALTH_ALERT',
        'System',
        'queue_monitor',
        { failedJobs: queueStats.failed },
      );
    }
    
    return queueStats.failed < 100;
  }
}
```

## 🔮 Future Enhancements

The codebase is designed for easy extension. Consider adding:

- **Payment Gateway Integration**: Stripe, PayPal, etc.
- **Webhook Support**: External service notifications
- **Authentication & Authorization**: JWT, role-based access
- **Rate Limiting**: API request throttling
- **Monitoring**: Health checks, metrics
- **Testing**: Comprehensive unit and integration tests
- **Caching**: Redis caching for frequently accessed data
- **Microservices**: Split into smaller, focused services

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify PostgreSQL is running
   - Check DATABASE_URL environment variable
   - Ensure database exists

2. **Redis Connection Failed**
   - Verify Redis server is running
   - Check Redis configuration in app.module.ts
   - Ensure Redis port 6379 is available

3. **Port Already in Use**
   - Change PORT environment variable
   - Kill existing processes on port 5000
   - Use different port number

### Logs

Check application logs for detailed error messages:
- Application logs: Console output
- Database logs: TypeORM query logging (in development)
- Queue logs: Bull job processing logs

## 📄 License

This project is licensed under the UNLICENSED license.

---

**Built with NestJS 🚀 | TypeScript 💪 | PostgreSQL 🐘 | Redis ⚡**
