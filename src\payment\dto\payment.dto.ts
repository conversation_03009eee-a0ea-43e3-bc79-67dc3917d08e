import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';

import { PaymentStatus } from '../../entities/payment.entity.js';

export class CreatePaymentDto {
	@ApiProperty({
		description: 'User ID',
		example: 12345,
	})
	@IsNumber()
	userId: number;

	@ApiPropertyOptional({
		description: 'Game ID',
		example: 678,
	})
	@IsNumber()
	@IsOptional()
	gameId?: number;

	@ApiProperty({
		description: 'Pack key',
		example: 'PACK-001',
	})
	@IsString()
	packKey: string;

	@ApiProperty({
		description: 'Payment method',
		example: 'credit_card',
	})
	@IsString()
	paymentMethod: string;

	@ApiProperty({
		description: 'Note',
		example: 'Payment for pack purchase',
	})
	@IsString()
	note: string;

	@ApiProperty({
		description: 'Payment amount in the specified currency',
		example: 99.99,
		minimum: 0.01,
	})
	@IsNumber()
	@Min(0.01)
	amount: number;

	@ApiPropertyOptional({
		description: 'Currency code (ISO 4217)',
		example: 'VND',
		default: 'VND',
	})
	@IsString()
	@IsOptional()
	currency?: string = 'VND';

	@ApiPropertyOptional({
		description: 'Payment status',
		enum: PaymentStatus,
		default: PaymentStatus.PENDING,
	})
	@IsEnum(PaymentStatus)
	@IsOptional()
	status?: PaymentStatus = PaymentStatus.PENDING;
}


export class UpdatePaymentDto {
	@ApiProperty({
		description: 'New payment status',
		enum: PaymentStatus,
		example: PaymentStatus.SUCCESS,
	})
	@IsEnum(PaymentStatus)
	status: PaymentStatus;
}
