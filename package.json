{"name": "fs_payment_service", "version": "1.0.0", "description": "FunStudio Payment Service", "author": "Funfinity <<EMAIL>>", "private": true, "license": "MIT", "type": "module", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "npx eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@infisical/sdk": "4.0.4", "@nestjs/bull": "11.0.3", "@nestjs/common": "11.0.1", "@nestjs/config": "4.0.2", "@nestjs/core": "11.0.1", "@nestjs/jwt": "11.0.0", "@nestjs/passport": "11.0.5", "@nestjs/platform-express": "11.0.1", "@nestjs/schedule": "6.0.0", "@nestjs/swagger": "11.2.0", "@nestjs/typeorm": "11.0.0", "@types/passport-jwt": "4.0.1", "bull": "4.16.5", "class-transformer": "0.5.1", "class-validator": "0.14.2", "dotenv": "17.2.2", "ioredis": "5.7.0", "jsonwebtoken": "9.0.2", "passport": "0.7.0", "passport-jwt": "4.0.1", "pg": "8.16.3", "reflect-metadata": "0.2.2", "rxjs": "7.8.1", "swagger-ui-express": "5.0.1", "typeorm": "0.3.26"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/cli": "11.0.0", "@nestjs/schematics": "11.0.0", "@nestjs/testing": "11.0.1", "@stylistic/eslint-plugin": "5.3.1", "@types/express": "5.0.0", "@types/jest": "30.0.0", "@types/node": "22.10.7", "@types/supertest": "6.0.2", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.2", "eslint-plugin-simple-import-sort": "12.1.1", "globals": "16.0.0", "jest": "30.0.0", "prettier": "3.4.2", "source-map-support": "0.5.21", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.7.3", "typescript-eslint": "8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}