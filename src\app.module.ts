import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import { databaseConfig } from './database/database.config.js';
import { PaymentModule } from './payment/payment.module.js';
import { QueueModule } from './queue/queue.module.js';
import { ScheduleModule as CustomScheduleModule } from './schedule/schedule.module.js';

@Module({
	imports: [
		TypeOrmModule.forRoot(databaseConfig),
		ScheduleModule.forRoot(),
		BullModule.forRoot({
			redis: {
				host: process.env.REDIS_HOST || 'localhost',
				port: Number(process.env.REDIS_PORT) || 6379,
			},
		}),
		PaymentModule,
		QueueModule,
		CustomScheduleModule,
	],
	controllers: [AppController],
	providers: [AppService],
})
export class AppModule {}
