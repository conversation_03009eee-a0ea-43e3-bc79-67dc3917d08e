import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from 'src/auth/auth.module.js';

import { PaymentTransactionEntity } from '../entities/payment.entity.js';
import { PaymentController } from './payment.controller.js';
import { PaymentService } from './payment.service.js';

@Module({
	imports: [TypeOrmModule.forFeature([PaymentTransactionEntity]), AuthModule],
	providers: [PaymentService],
	controllers: [PaymentController],
	exports: [PaymentService],
})
export class PaymentModule {}
