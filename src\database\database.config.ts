import { TypeOrmModuleOptions } from '@nestjs/typeorm';

import { PaymentTransactionEntity } from '../entities/payment.entity';

function buildDatabaseConfig(): TypeOrmModuleOptions {
	// If DATABASE_URL is provided, use it
	if (process.env.DATABASE_URL) {
		return {
			type: 'postgres',
			url: process.env.DATABASE_URL,
			entities: [PaymentTransactionEntity],
			synchronize: false, // Only for development - use migrations in production
			logging: process.env.NODE_ENV === 'development',
			ssl:
        process.env.NODE_ENV === 'production'
        	? { rejectUnauthorized: false }
        	: false,
		};
	}

	// Otherwise, use individual environment variables
	return {
		type: 'postgres',
		host: process.env.DB_HOST || 'localhost',
		port: Number(process.env.DB_PORT) || 5432,
		username: process.env.DB_USERNAME || 'postgres',
		password: process.env.DB_PASSWORD || 'postgres',
		database: process.env.DB_DATABASE || process.env.DB_NAME || 'payment_service',
		entities: [PaymentTransactionEntity],
		synchronize: false, // Only for development - use migrations in production
		logging: process.env.NODE_ENV === 'development',
		ssl:
      process.env.NODE_ENV === 'production'
      	? { rejectUnauthorized: false }
      	: false,
	};
}

export const databaseConfig: TypeOrmModuleOptions = buildDatabaseConfig();
