import type { CustomDecorator } from '@nestjs/common';
import {
	applyDecorators,
	SetMetadata,
	UseGuards,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ApiBearerAuth, ApiUnauthorizedResponse } from '@nestjs/swagger';
import type { RoleType } from 'src/constants/role';
import { AuthGuard } from 'src/guards/auth.guard';


export const PUBLIC_ROUTE_KEY = 'public_route';

export const PublicRoute = (isPublic = false): CustomDecorator =>
	SetMetadata(PUBLIC_ROUTE_KEY, isPublic);

export const Roles = Reflector.createDecorator<string[]>();

export function Auth(
	roles: RoleType[] = [],
	options?: Partial<{ public: boolean }>,
): MethodDecorator {
	const isPublicRoute = options?.public;

	return applyDecorators(
		Roles(roles),
		UseGuards(AuthGuard({ public: isPublicRoute })),
		ApiBearerAuth(),
		ApiUnauthorizedResponse({ description: 'Unauthorized' }),
		PublicRoute(isPublicRoute),
	);
}
