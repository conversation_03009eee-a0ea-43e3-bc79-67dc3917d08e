import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import type { Job } from 'bull';

import { PaymentStatus } from '../entities/payment.entity.js';
import { PaymentService } from '../payment/payment.service.js';
import { PaymentQueueJob } from './payment-queue.service.js';

@Processor('payment_queue')
export class PaymentQueueProcessor {
	private readonly logger = new Logger(PaymentQueueProcessor.name);

	constructor(
		private readonly paymentService: PaymentService,
	) {}

	@Process('process-payment')
	async handlePaymentProcess(job: Job<PaymentQueueJob>): Promise<void> {
		const { paymentId, packKey, action } = job.data;

		this.logger.log(
			`Processing payment job: ${action} for payment ${paymentId}`,
		);

		try {
			switch (action) {
				case 'PROCESS_PAYMENT':
					await this.processPayment(paymentId, packKey);
					break;
				case 'RETRY_PAYMENT':
					await this.retryPayment(paymentId, packKey);
					break;
				case 'NOTIFY_PAYMENT':
					await this.notifyPayment(paymentId, packKey);
					break;
			}

			// await this.transactionLogService.logAction(
			// 	`QUEUE_${action}_SUCCESS`,
			// 	'Payment',
			// 	paymentId,
			// 	{ packKey, jobId: job.id },
			// );

			this.logger.log(
				`Successfully processed payment job: ${action} for payment ${paymentId}`,
			);
		} catch(error: any) {
			this.logger.error(
				`Failed to process payment job: ${action} for payment ${paymentId}`,
				error,
			);

			// await this.transactionLogService.logAction(
			// 	`QUEUE_${action}_FAILED`,
			// 	'Payment',
			// 	paymentId,
			// 	{ packKey, jobId: job.id, error: (error as Error).message },
			// );

			throw error;
		}
	}

	private async processPayment(
		paymentId: string,
		packKey: string,
	): Promise<void> {
		// Simulate payment processing logic
		// In real implementation, this would integrate with payment gateway

		this.logger.log(`Processing payment for order ${packKey}`);

		// Simulate random success/failure for demo purposes
		const isSuccess = Math.random() > 0.3; // 70% success rate

		const newStatus = isSuccess
			? PaymentStatus.SUCCESS
			: PaymentStatus.FAILED;

		await this.paymentService.updateStatus(paymentId, {
			status: newStatus,
		});

		this.logger.log(
			`Payment ${paymentId} processed with status: ${newStatus}`,
		);
	}

	private async retryPayment(
		paymentId: string,
		packKey: string,
	): Promise<void> {
		this.logger.log(`Retrying payment for order ${packKey}`);
		await this.processPayment(paymentId, packKey);
	}

	private async notifyPayment(
		paymentId: string,
		packKey: string,
	): Promise<void> {
		// Simulate sending notification (email, SMS, webhook, etc.)
		this.logger.log(
			`Sending notification for payment ${paymentId}, order ${packKey}`,
		);

		// In real implementation, this would send actual notifications
		// For now, just log the action
		return Promise.resolve();
	}
}
