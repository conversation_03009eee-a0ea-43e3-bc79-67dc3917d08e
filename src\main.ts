import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as dotenv from 'dotenv';

// Load environment variables as early as possible
dotenv.config();

import { AppModule } from './app.module.js';
import { loadEnvFromInfisical } from './infisical-loader.js';

async function bootstrap() {
	const logger = new Logger('Bootstrap');

	// Setup Infisical
	await loadEnvFromInfisical();

	const app = await NestFactory.create(AppModule);

	// Enable global validation
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true,
		}),
	);

	// Enable CORS for development
	app.enableCors();

	// Swagger Configuration
	const config = new DocumentBuilder()
		.setTitle('FS Payment Service')
		.setDescription(
			'A comprehensive payment processing service with queue management, and scheduled tasks',
		)
		.setVersion('1.0')
		.addBearerAuth()
		.build();

	const document = SwaggerModule.createDocument(app, config);
	SwaggerModule.setup('api', app, document, {
		swaggerOptions: {
			persistAuthorization: true,
		},
	});

	const port = process.env.PORT || 5000;
	await app.listen(port, '0.0.0.0');

	logger.log(
		`🚀 FS Payment Service is running on http://0.0.0.0:${port}`,
	);
	logger.log(`📋 Swagger UI available at http://0.0.0.0:${port}/api`);
	logger.log(`📋 API JSON available at http://0.0.0.0:${port}/api-json`);
}

bootstrap().catch((error) => {
	const logger = new Logger('Bootstrap');
	logger.error('Failed to start application', error);
	process.exit(1);
});
